import {
  createModel,
  updateState,
  handleEffect,
} from '@jupiter/plugin-runtime/model';

import type { UserData as BaseUserData } from '@quality/common-apis/src/apis/api/base/base_data';
import {
  SpaceTypeBussiness,
  SpaceTypeDept,
  SpaceTypeReport,
  SpaceTypeMeasure,
  SpaceTypeExpert,
  SpaceTypeEmployee,
} from '@quality/common-apis/src/apis/api/insight/consts';
import {
  projectPermissionServiceClient,
  UserInfo,
} from '@quality/common-apis/src/apis/api/insight/permission';
import {
  ConfigEnum,
  configEnumServiceClient,
} from '@quality/common-apis/src/apis/api/metric_platform/config_enum';
import { metricDictServiceClient } from '@quality/common-apis/src/apis/api/metric_platform/dimension';
import { platformConfigServiceClient } from '@quality/common-apis/src/apis/api/metric_platform/platform_config';
import type { ProductInfo } from '@quality/common-apis/src/apis/api/product/product_model';
import type { UserData } from '@quality/common-apis/src/apis/api/user/user';
import { virtualDeptProductServiceClient } from '@quality/common-apis/src/apis/api/virtual_dept';
import { SpaceType } from '@quality/common-apis/src/apis/api/virtual_dept/space_type';

import { API_V1 } from '@/constants';
import { transformNodeTree } from '@/modules';
import { getNewRouters } from '@/routers';
import { RouteConfig } from '@/routers/interface';
import { getLoginUser } from '@/utils/login';

type GlobalModel = typeof globalModel;

interface GlobalState {
  loading: boolean;
  userInfo: UserData | null;
  authData: UserInfo | null;
  virtualSpaceList: SpaceType[] | null;
  newRoutes: RouteConfig[] | null;
  virtualTreeMaps: {
    [key: string]: {
      treeData: ProductInfo[];
      flatTreeData: ProductInfo[];
      isLoaded: boolean;
    };
  };
  // 全局的指标抽屉需要用到的参数
  globalMetricItem: {
    virtualSpaceKey: string | null;
  };
  nodeIdRecord: {
    [key: string]: ProductInfo;
  };
  businessTreeInfo: {
    data: ProductInfo[];
    pending: boolean;
    error: Error | null;
  };
  departmentTreeInfo: {
    data: ProductInfo[];
    pending: boolean;
    error: Error | null;
  };
  reportTreeInfo: {
    data: ProductInfo[];
    pending: boolean;
    error: Error | null;
  };
  measureTreeInfo: {
    data: ProductInfo[];
    pending: boolean;
    error: Error | null;
  };
  expertTreeInfo: {
    data: ProductInfo[];
    pending: boolean;
    error: Error | null;
  };
  employeeTreeInfo: {
    data: ProductInfo[];
    pending: boolean;
    error: Error | null;
  };
  configEnumInfo: {
    attrMaps: Array<{ show_name: string } & ConfigEnum>;
    activeAttrMaps: ConfigEnum[];
    problemRegionMaps: Array<{ show_name: string } & ConfigEnum>;
    activeProblemRegionMaps: ConfigEnum[];
  };
  platformConfig: {
    ch_enabled: boolean;
  };
  // metricDataMap: MetricDataMap;
  // metricDataMapTemplateId: string;
}

const routers_regex = /^\*#000#\*.*\*#000#\*$/;

const initialGlobalState: GlobalState = {
  loading: true,
  userInfo: null,
  authData: null,
  virtualSpaceList: [],
  newRoutes: [],
  virtualTreeMaps: {},
  globalMetricItem: {
    virtualSpaceKey: null,
  },
  nodeIdRecord: {},
  businessTreeInfo: {
    data: [],
    pending: false,
    error: null,
  },
  departmentTreeInfo: {
    data: [],
    pending: false,
    error: null,
  },
  reportTreeInfo: {
    data: [],
    pending: false,
    error: null,
  },
  measureTreeInfo: {
    data: [],
    pending: false,
    error: null,
  },
  expertTreeInfo: {
    data: [],
    pending: false,
    error: null,
  },
  employeeTreeInfo: {
    data: [],
    pending: false,
    error: null,
  },
  configEnumInfo: {
    attrMaps: [],
    activeAttrMaps: [],
    problemRegionMaps: [],
    activeProblemRegionMaps: [],
  },
  platformConfig: {
    ch_enabled: false,
  },
  // metricDataMap: new Map(),
  // metricDataMapTemplateId: '',
};

const globalModel = createModel<GlobalState>('devmind-global').define({
  state: initialGlobalState,
  actions: {
    setLoading: updateState.replace('loading'),
    setUserInfo: updateState.replace('userInfo'),
    setAuthData: updateState.replace('authData'),
    setNewRoutes: updateState.replace('newRoutes'),
    setVirtualSpaceList: updateState.replace('virtualSpaceList'),
    setVirtualTreeMaps: updateState.replace('virtualTreeMaps'),
    setGlobalMetricItem: updateState.replace('globalMetricItem'),
    setNodeIdRecord: updateState.replace('nodeIdRecord'),
    setConfigEnumInfo: updateState.replace('configEnumInfo'),
    setPlatformConfig: updateState.replace('platformConfig'),
    // setMetricDataMapTemplateId: updateState.replace('metricDataMapTemplateId'),
    getBusinessTree: handleEffect({ ns: 'businessTreeInfo', result: 'data' }),
    getDepartmentTree: handleEffect({
      ns: 'departmentTreeInfo',
      result: 'data',
    }),
    getReportTree: handleEffect({
      ns: 'reportTreeInfo',
      result: 'data',
    }),
    getExpertTree: handleEffect({
      ns: 'expertTreeInfo',
      result: 'data',
    }),
    getMeasureTree: handleEffect({
      ns: 'measureTreeInfo',
      result: 'data',
    }),
    getEmployeeTree: handleEffect({
      ns: 'employeeTreeInfo',
      result: 'data',
    }),
    getSpaceTypeList: handleEffect({
      ns: 'spaceTypeList',
      result: 'data',
    }),
    getVirtualTreeNodes: handleEffect({
      ns: 'virtualTreeNodes',
      result: 'data',
    }),
    addNodeRecord: (
      state,
      payload: { nodeId: string; nodeInfo: ProductInfo },
    ) => {
      state.nodeIdRecord[payload.nodeId] = payload.nodeInfo;
    },
    // setMetricDataMap: <T extends CHART_TYPE>(
    //   state,
    //   payload: {
    //     /* 指标卡区域 id，由前端生成维护，所有该指标卡区域下的指标卡均有一个 parent_id 和该 id 对应 */
    //     queryInfo: QueryOptions<T> & {
    //       metricId: string;
    //       freezeVersion: string | undefined;
    //     };

    //     data: QueryResponse;
    //   },
    // ) => {
    //   const { queryInfo, data } = payload;
    //   const key = genQueryReqKey({ ...queryInfo });
    //   state.metricDataMap.set(key, cloneDeep(data));
    // },
    // restoreMetricDataMap: state => {
    //   state.metricDataMap = new Map();
    // },
  },
  effects: context => {
    const globalModelContext = context as GlobalModel;
    return {
      getUserInfo: async () => {
        const userInfo = await getLoginUser();
        if (!userInfo) {
          return null;
        }
        globalModelContext.actions.setUserInfo(userInfo);
        return userInfo ?? null;
      },
      getAuthData: async () => {
        const res = await projectPermissionServiceClient.GetUserInfo({
          version: API_V1,
        });
        globalModelContext.actions.setAuthData(res.data ?? null);
        return res.data ?? null;
      },
      getDepartmentTree: async () => {
        const res = await metricDictServiceClient.GetSpaceTree({
          space_type: SpaceTypeDept,
          version: API_V1,
        });
        globalModelContext.actions.addVirtualTreeNodes(
          SpaceTypeDept,
          res?.data?.data ?? [],
        );
        return res?.data?.data ?? [];
      },
      getBusinessTree: async () => {
        /* IFTRUE_prodEnv */
        const res = await metricDictServiceClient.GetSpaceTree({
          space_type: SpaceTypeBussiness,
          version: API_V1,
        });

        return res?.data?.data ?? [];
        /* FITRUE_openEnv */
      },

      getReportTree: async () => {
        /* IFTRUE_prodEnv */
        const res = await metricDictServiceClient.GetSpaceTree({
          space_type: SpaceTypeReport,
          version: API_V1,
        });
        return res?.data?.data ?? [];
        /* FITRUE_openEnv */
      },
      getMeasureTree: async () => {
        /* IFTRUE_prodEnv */
        const res = await metricDictServiceClient.GetSpaceTree({
          space_type: SpaceTypeMeasure,
          version: API_V1,
        });
        return res?.data?.measure_data ?? [];
        /* FITRUE_openEnv */
      },
      getExpertTree: async () => {
        const res = await metricDictServiceClient.GetSpaceTree({
          space_type: SpaceTypeExpert,
          version: API_V1,
        });
        globalModelContext.actions.addVirtualTreeNodes(
          SpaceTypeExpert,
          res.data?.data ?? [],
        );
        return res?.data?.data ?? [];
      },
      getEmployeeTree: async () => {
        const res = await metricDictServiceClient.GetSpaceTree({
          space_type: SpaceTypeEmployee,
          version: API_V1,
        });
        return res?.data.data ?? [];
      },
      getSpaceTypeList: async () => {
        try {
          console.log('🔄 开始获取空间类型列表...');
          const res = await virtualDeptProductServiceClient.ListSpaceType({
            version: API_V1,
            ListFilter: {
              PageSize: 100,
              PageNumber: 1,
              Status: true,
            },
          });

          console.log('📡 API响应:', {
            code: res.code,
            message: res.msg,
            dataExists: Boolean(res.data),
            itemListLength: res.data?.ItemList?.length || 0,
            itemList: res.data?.ItemList,
          });

          const isAdmin = globalModelContext.state.authData?.isAdmin;
          console.log('👤 用户权限信息:', {
            isAdmin,
            authData: globalModelContext.state.authData,
          });

          if (!res.data || !res.data.ItemList) {
            console.warn('⚠️ API返回数据为空或格式不正确');
            globalModelContext.actions.setVirtualSpaceList([]);
            globalModelContext.actions.setNewRoutes(getNewRouters([]));
            return [];
          }

          const _filterList = isAdmin
            ? res.data.ItemList
            : res.data.ItemList.filter(item => {
                const shouldFilter = routers_regex.test(item.NameZh);
                console.log(`🔍 过滤检查 "${item.NameZh}":`, {
                  regex: routers_regex.toString(),
                  shouldFilter,
                  kept: !shouldFilter,
                });
                return !shouldFilter;
              });

          console.log('✅ 最终空间列表:', {
            originalLength: res.data.ItemList.length,
            filteredLength: _filterList.length,
            filterList: _filterList,
          });

          globalModelContext.actions.setVirtualSpaceList(_filterList);
          globalModelContext.actions.setNewRoutes(getNewRouters(_filterList));
          return res.data;
        } catch (err) {
          console.error('❌ 获取空间类型列表失败:', err);
          globalModelContext.actions.setVirtualSpaceList([]);
          globalModelContext.actions.setNewRoutes(getNewRouters([]));
          return [];
        }
      },
      // 进入页面读取url的spaceType，初始化node信息,防止node_info没法同步获取
      getInitNodes: async space_type => {
        if (!space_type) {
          return;
        }
        const res = await metricDictServiceClient.GetSpaceTree({
          space_type,
          version: API_V1,
        });
        globalModelContext.actions.addVirtualTreeNodes(
          space_type,
          res.data?.data ?? [],
        );
        return res?.data.data ?? [];
      },
      getVirtualTreeNodes: async spaceType => {
        let res = await metricDictServiceClient.GetSpaceTree(
          {
            version: API_V1,
            space_type: spaceType,
          },
          {
            hideNotice: true,
          },
        );
        if (res.data?.data && res.code === 200) {
          return res.data.data;
        } else {
          return [];
        }
      },
      addVirtualTreeNodes: (spaceType: string, virtualNodes: ProductInfo[]) => {
        const flatTreeData = transformNodeTree(virtualNodes).flatTree;
        const treeMaps = {
          ...globalModelContext.state.virtualTreeMaps,
          [spaceType]: {
            treeData: virtualNodes,
            flatTreeData,
            isLoaded: true,
          },
        };
        globalModelContext.actions.addNodeRecords(
          flatTreeData.map(item => item.node_id),
          flatTreeData,
        );
        globalModelContext.actions.setVirtualTreeMaps(treeMaps);
      },
      // 这个方法默认可以set多个noderecord进入全局
      addNodeRecords(ids: string[], nodeInfos: ProductInfo[]) {
        const nodeIdRecord = globalModelContext.state.nodeIdRecord;
        ids.forEach((nodeId, idx) => {
          nodeIdRecord[nodeId] = nodeInfos[idx];
        });
        globalModelContext.actions.setNodeIdRecord(nodeIdRecord);
      },
      getPlatformConfig: async () => {
        const res = await platformConfigServiceClient.GetPlatformConfig();
        globalModelContext.actions.setPlatformConfig({
          ch_enabled: res?.data?.ch_enabled,
        });
      },
      getConfigEnum: async () => {
        const [problemRegionRes, attrRes] = await Promise.all([
          configEnumServiceClient.ListConfigEnum({
            version: API_V1,
            config_type: 'problem_region',
          }),
          configEnumServiceClient.ListConfigEnum({
            version: API_V1,
            config_type: 'attr',
          }),
        ]);
        globalModelContext.actions.setConfigEnumInfo({
          attrMaps:
            attrRes?.data?.items.map(item => {
              return {
                ...item,
                show_name: `${item.display_name}${
                  item.status === 0 ? '  (已下线)' : ''
                }`,
              };
            }) || [],
          activeAttrMaps: attrRes?.data?.items.filter(
            item => item.status === 1,
          ),
          problemRegionMaps:
            problemRegionRes?.data?.items.map(item => {
              return {
                ...item,
                show_name: `${item.display_name}${
                  item.status === 0 ? '  (已下线)' : ''
                }`,
              };
            }) || [],
          activeProblemRegionMaps: problemRegionRes?.data?.items.filter(
            item => item.status === 1,
          ),
        });
      },
    };
  },
  computed: {
    baseUserData: [
      state => state.userInfo,
      (userInfo: UserData): BaseUserData => {
        const { avatar = '', username = '', name = '' } = userInfo ?? {};
        return { avatar_url: avatar, username, name };
      },
    ],
  },
});

export default globalModel;
