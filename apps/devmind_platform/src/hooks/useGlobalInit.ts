import { useState } from 'react';

import { useAsyncEffect } from '@byted/hooks';
import { useModel } from '@jupiter/plugin-runtime/model';

import {
  getAllTimeValue,
  getDefaultTimeValue,
  getDoubleMounthTimeValue,
} from '@/components/TimeFilter';
import { useQueryPatch } from '@/hooks/url/useQueryPatch';
import { SpaceType } from '@/interface';
import globalModel from '@/model/global';
import { NodeManager } from '@/modules/node';

import { getVirtualSpaceType } from './useSpaceType';

const useGlobalInit = () => {
  useQueryPatch();
  const [treeRes, setTreeRes] = useState<any>();
  window.defaultTimeValue = getDefaultTimeValue();
  window.doubleMounthTimeValue = getDoubleMounthTimeValue();
  window.allTimeValue = getAllTimeValue();

  const [
    { loading, authData, userInfo },
    {
      setLoading,
      getUserInfo,
      getAuthData,
      getConfigEnum,
      getPlatformConfig,
      getExpertTree,
      getSpaceTypeList,
      getInitNodes,
    },
  ] = useModel([
    globalModel,
    state => ({
      loading: state.loading,
      authData: state.authData,
      userInfo: state.userInfo,
    }),
  ]);

  useAsyncEffect(async () => {
    // 初始化树节点信息Record
    console.log('🔍 useGlobalInit 条件检查:', {
      treeRes: !!treeRes,
      userInfo: !!userInfo,
      authData: !!authData,
      allConditionsMet: !!(treeRes && userInfo && authData)
    });

    if (treeRes && userInfo && authData) {
      console.log('✅ 所有条件满足，开始初始化...');
      NodeManager.create(
        ...treeRes.map(({ value, spaceType }) => ({ value, spaceType })),
      );
      console.log('🚀 即将调用 getSpaceTypeList...');
      await getSpaceTypeList();
      console.log('✅ getSpaceTypeList 调用完成');
      setLoading(false);
    } else {
      console.log('❌ 条件不满足，跳过初始化');
    }
  }, [treeRes, userInfo, authData]);

  const initFunc = async () => {
    const userInfo = await getUserInfo();
    if (!userInfo.value) {
      return;
    }
    const [res, _, _1, _2, initNodes] = await Promise.all([
      getExpertTree(),
      getAuthData(),
      getConfigEnum(),
      getPlatformConfig(),
      getInitNodes(getVirtualSpaceType()),
    ]);
    setTreeRes([
      { ...res, spaceType: SpaceType.Expert },
      { ...initNodes, spaceType: getVirtualSpaceType() },
    ]);
  };

  useAsyncEffect(async () => {
    // efd 在html里异步的注册sdk，设置__SSOType
    if (FE_ENV === 'efd' && !(window as any)?.__SSOType) {
      const timeInterval = setInterval(async () => {
        // 防止 __SSOType还没有初始化
        if ((window as any)?.__SSOType) {
          await initFunc();
          clearInterval(timeInterval);
        }
      }, 200);
      return;
    }
    await initFunc();
  }, []);

  return !loading && userInfo !== undefined;
};

export default useGlobalInit;
